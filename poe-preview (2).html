<!DOCTYPE html><html lang="en"><head><meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://code.jquery.com https://unpkg.com https://d3js.org https://threejs.org https://cdn.plot.ly https://stackpath.bootstrapcdn.com https://maps.googleapis.com https://cdn.tailwindcss.com https://ajax.googleapis.com https://kit.fontawesome.com https://cdn.datatables.net https://maxcdn.bootstrapcdn.com https://code.highcharts.com https://tako-static-assets-production.s3.amazonaws.com https://www.youtube.com https://fonts.googleapis.com https://fonts.gstatic.com https://pfst.cf2.poecdn.net https://puc.poecdn.net https://i.imgur.com https://wikimedia.org https://*.icons8.com https://*.giphy.com https://picsum.photos https://images.unsplash.com; frame-src 'self' https://www.youtube.com https://trytako.com; child-src 'self'; manifest-src 'self'; worker-src 'self'; upgrade-insecure-requests; block-all-mixed-content;">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ByteWise - Customizable AI Chatbots for Education</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer=""></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0EA5E9',
                        'primary-dark': '#0284C7',
                        'primary-light': '#38BDF8',
                        'tech-dark': '#0283dd',
                        'tech-electric': '#0EA5E9',
                        'tech-cyan': '#06e9c1',
                        'tech-blue': '#0F172A',
                        'tech-accent': '#00D4FF',
                        'bg-light': '#FFFFFF',
                        'bg-dark': '#0F172A',
                        'text-light': '#1F2937',
                        'text-dark': '#F9FAFB'
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700&family=Work+Sans:wght@100;200;300;400;500;600;700&family=Montserrat:wght@100;200;300;400;500;600;700&family=Raleway:wght@100;200;300;400;500;600;700&family=Lato:wght@100;300;400;700&family=Source+Sans+Pro:wght@200;300;400;600;700&family=Nunito+Sans:wght@200;300;400;600;700&family=Playfair+Display:wght@300;400;500;600;700&family=Cormorant+Garamond:wght@300;400;500;600;700&family=Crimson+Text:wght@300;400;600&family=Libre+Baskerville:wght@300;400;700&family=Lora:wght@300;400;500;600;700&family=Merriweather:wght@300;400;700&family=Georgia&family=Cardo:wght@400;700&display=swap');
        body { font-family: 'Work Sans', sans-serif; }
        
        .font-poppins { font-family: 'Poppins', sans-serif; font-weight: 200; letter-spacing: -0.02em; }
        .font-montserrat { font-family: 'Montserrat', sans-serif; font-weight: 200; letter-spacing: -0.01em; }
        .font-raleway { font-family: 'Raleway', sans-serif; font-weight: 200; letter-spacing: 0.01em; }
        .font-lato { font-family: 'Lato', sans-serif; font-weight: 300; letter-spacing: -0.01em; }
        .font-source { font-family: 'Source Sans Pro', sans-serif; font-weight: 200; letter-spacing: 0.01em; }
        .font-nunito { font-family: 'Nunito Sans', sans-serif; font-weight: 200; letter-spacing: -0.01em; }
        
        .elegant-font { font-family: 'Playfair Display', serif; font-weight: 400; letter-spacing: -0.01em; }
        .gradient-bg { background: linear-gradient(135deg, #0EA5E9 0%, #06B6D4 50%, #00D4FF 100%); }
        .tech-gradient-bg { background: linear-gradient(135deg, #0F172A 0%, #1E40AF 50%, #0EA5E9 100%); }
        .smooth-tech-gradient {
            background: linear-gradient(90deg, #1E40AF 0%, #3B82F6 20%, #6366F1 40%, #8B5CF6 60%, #A855F7 80%, #EC4899 100%);
            background-clip: text; -webkit-background-clip: text; -webkit-text-fill-color: transparent;
        }
        
        .gradient-border { position: relative; background: white; border: 2px solid transparent; background-clip: padding-box; }
        .gradient-border::before {
            content: ''; position: absolute; top: 0; right: 0; bottom: 0; left: 0; z-index: -1; margin: -2px;
            border-radius: inherit; background: linear-gradient(90deg, #1E40AF 0%, #3B82F6 20%, #6366F1 40%, #8B5CF6 60%, #A855F7 80%, #EC4899 100%);
        }
        
        .dark .gradient-border { background: #1F2937; }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-5px); box-shadow: 0 20px 40px rgba(14, 165, 233, 0.15); }
        .animate-float { animation: float 6s ease-in-out infinite; }
        @keyframes float { 0%, 100% { transform: translateY(0px); } 50% { transform: translateY(-20px); } }
        .dark .bg-light { background-color: #181818; }
        .dark .text-light { color: #F9FAFB; }
        
        .editable-section { position: relative; }
        .editable-section:hover .edit-controls { opacity: 1; }
        .edit-controls { 
            opacity: 0; 
            transition: opacity 0.3s ease;
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
        }
        .editing { outline: 2px dashed #0EA5E9; outline-offset: 4px; }
        [contenteditable="true"] { min-height: 20px; }
        [contenteditable="true"]:focus { outline: 2px solid #0EA5E9; outline-offset: 2px; background: rgba(14, 165, 233, 0.05); }
        
        .download-btn {
            background: #0EA5E9;
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
            transition: all 0.3s ease;
            white-space: nowrap;
        }
        .download-btn:hover {
            background: #0284C7;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(14, 165, 233, 0.4);
        }

        .fixed-controls {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body class="bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300">
    <div x-data="editableApp()" class="min-h-screen">

        <!-- Fixed Controls -->
        <div class="fixed-controls">
            <div class="flex flex-col gap-2 items-end">
                <!-- Save Status Indicator -->
                <div x-show="saveStatus" x-transition="" class="bg-green-500 text-white px-3 py-2 rounded-lg text-sm shadow-lg">
                    <i class="fas fa-check-circle"></i> <span x-text="saveStatus"></span>
                </div>

                <!-- Download Button -->
                <button @click="downloadHTML()" class="download-btn">
                    <i class="fas fa-download"></i> Download HTML
                </button>
            </div>
        </div>

        <!-- Header -->
        <header id="header-section" class="editable-section sticky top-0 z-50 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700">
            <div class="edit-controls">
                <button @click="saveSection('header-section')" class="save-btn bg-primary text-white px-3 py-1 rounded text-sm hover:bg-primary-dark transition-colors">
                    <i class="fas fa-save"></i> Save
                </button>
            </div>
            <nav class="container mx-auto px-4 py-4">
                <div class="flex items-center justify-between">
                    <!-- Logo -->
                    <div class="flex items-center space-x-2">
                        <div class="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                            <i class="fas fa-robot text-white text-lg"></i>
                        </div>
                        <span class="text-2xl font-bold smooth-tech-gradient" data-editable="">ByteWise</span>
                    </div>

                    <!-- Desktop Navigation -->
                    <div class="hidden md:flex items-center space-x-8">
                        <a href="#home" class="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors" data-editable-link="">Home</a>
                        <a href="#students-section" class="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors" data-editable-link="">Students</a>
                        <a href="#teachers-section" class="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors" data-editable-link="">Teachers</a>
                        <a href="#community-section" class="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors" data-editable-link="">Community</a>
                    </div>

                    <!-- CTA Buttons -->
                    <div class="hidden md:flex items-center space-x-4">
                        <a href="https://new.bytewise.hk/#/start" class="px-4 py-2 text-primary gradient-border rounded-lg hover:bg-primary hover:text-white transition-all text-center block" data-editable-link="">
                            Login
                        </a>
                        <a href="https://new.bytewise.hk/#/register" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-all text-center block" data-editable-link="">
                            Register
                        </a>
                    </div>

                    <!-- Mobile Menu Button -->
                    <button @click="mobileMenu = !mobileMenu" class="md:hidden p-2">
                        <i class="fas fa-bars text-gray-600 dark:text-gray-300"></i>
                    </button>
                </div>

                <!-- Mobile Menu -->
                <div x-show="mobileMenu" x-transition="" class="md:hidden mt-4 pb-4 border-t border-gray-200 dark:border-gray-700 pt-4">
                    <div class="flex flex-col space-y-4">
                        <a href="#home" class="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors" data-editable-link="">Home</a>
                        <a href="#students-section" class="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors" data-editable-link="">Students</a>
                        <a href="#teachers-section" class="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors" data-editable-link="">Teachers</a>
                        <a href="#community-section" class="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors" data-editable-link="">Community</a>
                        <div class="flex flex-col space-y-2 pt-4">
                            <a href="https://new.bytewise.hk/#/start" class="px-4 py-2 text-primary border border-primary rounded-lg hover:bg-primary hover:text-white transition-all text-center block" data-editable-link="">
                                Login
                            </a>
                            <a href="https://new.bytewise.hk/#/register" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-all text-center block" data-editable-link="">
                                Register
                            </a>
                        </div>
                    </div>
                </div>
            </nav>
        </header>

        <!-- Hero Section -->
        <section id="hero-section" class="editable-section py-20 lg:py-32 bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-slate-900 dark:to-blue-900">
            <div class="edit-controls">
                <button @click="saveSection('hero-section')" class="save-btn bg-primary text-white px-3 py-1 rounded text-sm hover:bg-primary-dark transition-colors">
                    <i class="fas fa-save"></i> Save
                </button>
            </div>
            <div class="container mx-auto px-4">
                <div class="grid lg:grid-cols-2 gap-12 items-center">
                    <div class="space-y-8">
                        <h1 class="text-5xl lg:text-6xl leading-tight elegant-font">
                            <span class="smooth-tech-gradient" data-editable="">
                                Customizable AI Chatbots for Education
                            </span>
                        </h1>
                        <p class="text-xl text-gray-600 dark:text-gray-300 leading-relaxed" data-editable="">
                            Empower students and educators with intelligent, personalized learning experiences powered by cutting-edge AI technology. Our adaptive solutions analyze performance data to adjust lesson plans, ensuring each learner is challenged and supported at the optimal level for maximum engagement and retention.
                        </p>
                        <div class="flex flex-col sm:flex-row gap-4">
                            <a href="#" class="px-8 py-4 bg-primary text-white rounded-xl hover:bg-primary-dark transition-all transform hover:scale-105 font-medium text-lg text-center block" data-editable-link="">
                                Explore ByteWise Now
                                <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                            <a href="#" class="px-8 py-4 text-gray-700 dark:text-gray-300 hover:text-primary transition-all font-medium text-lg text-center block" data-editable-link="">
                                Learn more
                            </a>
                        </div>
                    </div>
                    
                    <div class="relative">
                        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl p-8 animate-float">
                            <div class="space-y-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                                        <i class="fas fa-robot text-white text-sm"></i>
                                    </div>
                                    <span class="font-medium" data-editable="">AI Assistant</span>
                                </div>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                    <p class="text-sm" data-editable="">Hi! I'm your personalized learning assistant. How can I help you with your studies today?</p>
                                </div>
                                <div class="flex space-x-2">
                                    <a href="#" class="px-3 py-2 bg-primary/10 text-primary rounded-lg text-sm hover:bg-primary/20 transition-colors cursor-pointer" data-editable-link="">Explain Concepts</a>
                                    <a href="#" class="px-3 py-2 bg-primary/10 text-primary rounded-lg text-sm hover:bg-primary/20 transition-colors cursor-pointer" data-editable-link="">Practice Quiz</a>
                                    <a href="#" class="px-3 py-2 bg-primary/10 text-primary rounded-lg text-sm hover:bg-primary/20 transition-colors cursor-pointer" data-editable-link="">Study Plan</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Overview -->
        <section id="features-section" class="editable-section py-20 bg-white dark:bg-gray-900">
            <div class="edit-controls">
                <button @click="saveSection('features-section')" class="save-btn bg-primary text-white px-3 py-1 rounded text-sm hover:bg-primary-dark transition-colors">
                    <i class="fas fa-save"></i> Save
                </button>
            </div>
            <div class="container mx-auto px-4">
                <div class="grid md:grid-cols-4 gap-8">
                    <div class="text-center space-y-4 card-hover p-6 rounded-xl">
                        <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto">
                            <i class="fas fa-graduation-cap text-primary text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-lg elegant-font" data-editable="">Personalized Learning</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm" data-editable="">AI tailors learning paths to match individual students' cognitive patterns and progress rhythms</p>
                    </div>
                    
                    <div class="text-center space-y-4 card-hover p-6 rounded-xl">
                        <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto">
                            <i class="fas fa-chalkboard-teacher text-primary text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-lg elegant-font" data-editable="">Teacher Tools</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm" data-editable="">Centralized course management hub with curriculum mapping and workflow automation</p>
                    </div>
                    
                    <div class="text-center space-y-4 card-hover p-6 rounded-xl">
                        <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto">
                            <i class="fas fa-comments text-primary text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-lg elegant-font" data-editable="">Interactive Chat</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm" data-editable="">Subject-specific AI tutoring via contextual dialogue interfaces</p>
                    </div>
                    
                    <div class="text-center space-y-4 card-hover p-6 rounded-xl">
                        <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto">
                            <i class="fas fa-chart-line text-primary text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-lg elegant-font" data-editable="">Analytics</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm" data-editable="">Data-driven progress tracking with predictive learning gap identification</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- For Students Section -->
        <section id="students-section" class="editable-section py-20 bg-gray-50 dark:bg-gray-800">
            <div class="edit-controls">
                <button @click="saveSection('students-section')" class="save-btn bg-primary text-white px-3 py-1 rounded text-sm hover:bg-primary-dark transition-colors">
                    <i class="fas fa-save"></i> Save
                </button>
            </div>
            <div class="container mx-auto px-4">
                <div class="text-center mb-16">
                    <h2 class="text-4xl lg:text-5xl mb-6 elegant-font">
                        <span class="smooth-tech-gradient" data-editable="">AI for Student Success</span>
                    </h2>
                    <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto" data-editable="">
                        Smart AI assistant providing personalized learning support across all HKBU disciplines to help you achieve academic excellence
                    </p>
                </div>

                <!-- Course Categories Grid -->
                <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
                    <!-- Main Faculties -->
                    <a href="#main-faculties" class="bg-white dark:bg-gray-900 rounded-lg p-6 shadow-md hover:shadow-lg transition-all duration-300 card-hover cursor-pointer transform hover:scale-105 block" data-editable-link="">
                        <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mb-4 mx-auto">
                            <i class="fas fa-university text-blue-600 dark:text-blue-400 text-lg"></i>
                        </div>
                        <h4 class="font-semibold text-base mb-2 text-center" data-editable="">Main Faculties</h4>
                        <p class="text-gray-600 dark:text-gray-300 text-xs text-center leading-relaxed" data-editable="">Business, Science, Communication, Arts, Social Sciences, Computer Science, Visual Arts, Music</p>
                    </a>

                    <!-- General Education -->
                    <a href="#general-education" class="bg-white dark:bg-gray-900 rounded-lg p-6 shadow-md hover:shadow-lg transition-all duration-300 card-hover cursor-pointer transform hover:scale-105 block" data-editable-link="">
                        <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center mb-4 mx-auto">
                            <i class="fas fa-graduation-cap text-yellow-600 dark:text-yellow-400 text-lg"></i>
                        </div>
                        <h4 class="font-semibold text-base mb-2 text-center" data-editable="">General Education (GE)</h4>
                        <p class="text-gray-600 dark:text-gray-300 text-xs text-center leading-relaxed" data-editable="">Critical Thinking, Cultural Studies, Ethics, Philosophy, History</p>
                    </a>

                    <!-- Language Courses -->
                    <a href="#language-courses" class="bg-white dark:bg-gray-900 rounded-lg p-6 shadow-md hover:shadow-lg transition-all duration-300 card-hover cursor-pointer transform hover:scale-105 block" data-editable-link="">
                        <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mb-4 mx-auto">
                            <i class="fas fa-globe text-green-600 dark:text-green-400 text-lg"></i>
                        </div>
                        <h4 class="font-semibold text-base mb-2 text-center" data-editable="">Language Courses</h4>
                        <p class="text-gray-600 dark:text-gray-300 text-xs text-center leading-relaxed" data-editable="">English, Chinese, Japanese, Korean, French, German, Spanish</p>
                    </a>

                    <!-- Other Courses -->
                    <a href="#other-courses" class="bg-white dark:bg-gray-900 rounded-lg p-6 shadow-md hover:shadow-lg transition-all duration-300 card-hover cursor-pointer transform hover:scale-105 block" data-editable-link="">
                        <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mb-4 mx-auto">
                            <i class="fas fa-puzzle-piece text-purple-600 dark:text-purple-400 text-lg"></i>
                        </div>
                        <h4 class="font-semibold text-base mb-2 text-center" data-editable="">Other Courses</h4>
                        <p class="text-gray-600 dark:text-gray-300 text-xs text-center leading-relaxed" data-editable="">Physical Education, Interdisciplinary Studies, Special Programs, Electives</p>
                    </a>
                </div>

                <!-- AI Features - Main Focus -->
                <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-xl p-8 lg:p-12">
                    <h3 class="text-3xl font-semibold mb-8 text-center elegant-font" data-editable="">How AI Helps You Learn Better</h3>
                    <div class="grid md:grid-cols-3 gap-8 items-stretch">
                        <div class="text-center">
                            <div class="w-20 h-20 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-6">
                                <i class="fas fa-question-circle text-primary text-3xl"></i>
                            </div>
                            <h4 class="font-semibold text-lg mb-3" data-editable="">Instant Q&amp;A</h4>
                            <p class="text-gray-600 dark:text-gray-300 mb-4" data-editable="">24/7 answers to your academic questions</p>
                            <div class="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-lg p-4 text-xs leading-relaxed border-l-4 border-primary shadow-sm hover:shadow-md transition-all duration-300 transform hover:scale-105">
                                <p class="font-medium text-primary mb-2 flex items-center"><i class="fas fa-lightbulb mr-2 text-yellow-500"></i>Use Case:</p>
                                <p class="text-gray-700 dark:text-gray-200 italic" data-editable="">"Stuck on UCLC 1008 essay structure at 2 AM? AI breaks down thesis &amp; evidence placement using your course readings, fast."</p>
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="w-20 h-20 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-6">
                                <i class="fas fa-calendar-alt text-primary text-3xl"></i>
                            </div>
                            <h4 class="font-semibold text-lg mb-3" data-editable="">Study Planning</h4>
                            <p class="text-gray-600 dark:text-gray-300 mb-4" data-editable="">Personalized study schedules and timelines</p>
                            <div class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4 text-xs leading-relaxed border-l-4 border-green-500 shadow-sm hover:shadow-md transition-all duration-300 transform hover:scale-105">
                                <p class="font-medium text-green-600 mb-2 flex items-center"><i class="fas fa-calendar-check mr-2 text-green-500"></i>Use Case:</p>
                                <p class="text-gray-700 dark:text-gray-200 italic" data-editable="">"20-pg history paper due? AI timelines: Week1-source via prof-recommended databases, Week3-outline per course reqs."</p>
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="w-20 h-20 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-6">
                                <i class="fas fa-chart-bar text-primary text-3xl"></i>
                            </div>
                            <h4 class="font-semibold text-lg mb-3" data-editable="">Learning Analytics</h4>
                            <p class="text-gray-600 dark:text-gray-300 mb-4" data-editable="">Track progress and improve performance</p>
                            <div class="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-4 text-xs leading-relaxed border-l-4 border-purple-500 shadow-sm hover:shadow-md transition-all duration-300 transform hover:scale-105">
                                <p class="font-medium text-purple-600 mb-2 flex items-center"><i class="fas fa-chart-line mr-2 text-purple-500"></i>Use Case:</p>
                                <p class="text-gray-700 dark:text-gray-200 italic" data-editable="">"Stats linear regression confusing? AI finds formula mix-ups (e.g., indep/dep variables) in quizzes, links to lecture notes for review."</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- For Teachers Section -->
        <section id="teachers-section" class="editable-section py-20 bg-white dark:bg-gray-900">
            <div class="edit-controls">
                <button @click="saveSection('teachers-section')" class="save-btn bg-primary text-white px-3 py-1 rounded text-sm hover:bg-primary-dark transition-colors">
                    <i class="fas fa-save"></i> Save
                </button>
            </div>
            <div class="container mx-auto px-4">
                <div class="text-center mb-16">
                    <h2 class="text-4xl lg:text-5xl mb-6 elegant-font">
                        <span class="smooth-tech-gradient" data-editable="">Empower Teachers with AI</span>
                    </h2>
                    <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto" data-editable="">
                        Create custom chatbots, automate administrative tasks, and gain insights into student performance.
                    </p>
                </div>

                <div class="grid lg:grid-cols-3 gap-8">
                    <div class="bg-gray-50 dark:bg-gray-800 rounded-2xl p-8 card-hover">
                        <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mb-6">
                            <i class="fas fa-robot text-primary text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-xl mb-4 elegant-font" data-editable="">Custom Chatbots</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-6" data-editable="">Design AI assistants tailored to your curriculum and teaching style.</p>
                        <ul class="space-y-2 text-sm">
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">Subject-specific knowledge</span>
                            </li>
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">Custom personality</span>
                            </li>
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">Integration with materials</span>
                            </li>
                        </ul>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-800 rounded-2xl p-8 card-hover">
                        <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mb-6">
                            <i class="fas fa-chart-bar text-primary text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-xl mb-4 elegant-font" data-editable="">Student Analytics</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-6" data-editable="">Get detailed insights into student engagement and learning patterns.</p>
                        <ul class="space-y-2 text-sm">
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">Performance tracking</span>
                            </li>
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">Engagement metrics</span>
                            </li>
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">Learning gap identification</span>
                            </li>
                        </ul>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-800 rounded-2xl p-8 card-hover">
                        <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mb-6">
                            <i class="fas fa-cogs text-primary text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-xl mb-4 elegant-font" data-editable="">Automation Tools</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-6" data-editable="">Automate repetitive tasks and focus on what matters most - teaching.</p>
                        <ul class="space-y-2 text-sm">
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">Auto-grading</span>
                            </li>
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">FAQ responses</span>
                            </li>
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">Progress reports</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Community Section -->
        <section id="community-section" class="editable-section py-20 bg-primary/5 dark:bg-gray-800">
            <div class="edit-controls">
                <button @click="saveSection('community-section')" class="save-btn bg-primary text-white px-3 py-1 rounded text-sm hover:bg-primary-dark transition-colors">
                    <i class="fas fa-save"></i> Save
                </button>
            </div>
            <div class="container mx-auto px-4">
                <div class="text-center mb-16">
                    <h2 class="text-4xl lg:text-5xl mb-6 elegant-font">
                        <span class="smooth-tech-gradient" data-editable="">Join the Community</span>
                    </h2>
                    <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto" data-editable="">
                        Connect with educators and students from Hong Kong and beyond who are transforming education with AI.
                    </p>
                </div>

                <div class="bg-white dark:bg-gray-900 rounded-2xl p-8 shadow-xl mt-12">
                    <div class="grid md:grid-cols-3 gap-8">
                        <div class="text-center">
                            <div class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-university text-primary text-2xl"></i>
                            </div>
                            <h3 class="font-semibold text-lg mb-2" data-editable="">School of Business</h3>
                            <p class="text-gray-600 dark:text-gray-300 text-sm" data-editable="">"ByteWise has revolutionized our business curriculum with AI-powered market analysis and personalized learning paths."</p>
                        </div>
                        
                        <div class="text-center">
                            <div class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-school text-primary text-2xl"></i>
                            </div>
                            <h3 class="font-semibold text-lg mb-2" data-editable="">General Education</h3>
                            <p class="text-gray-600 dark:text-gray-300 text-sm" data-editable="">"The interdisciplinary AI tutors help students connect knowledge across different fields and develop critical thinking skills."</p>
                        </div>
                        
                        <div class="text-center">
                            <div class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-graduation-cap text-primary text-2xl"></i>
                            </div>
                            <h3 class="font-semibold text-lg mb-2" data-editable="">Centre of Innovative Service-Learning</h3>
                            <p class="text-gray-600 dark:text-gray-300 text-sm" data-editable="">"The analytics help us identify struggling students early and provide targeted support."</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer id="footer-section" class="editable-section py-16 bg-gray-900 text-gray-300">
            <div class="edit-controls">
                <button @click="saveSection('footer-section')" class="save-btn bg-primary text-white px-3 py-1 rounded text-sm hover:bg-primary-dark transition-colors">
                    <i class="fas fa-save"></i> Save
                </button>
            </div>
            <div class="container mx-auto px-4">
                <div class="grid md:grid-cols-2 gap-12 mb-8">
                    <div class="space-y-4">
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                                <i class="fas fa-robot text-white"></i>
                            </div>
                            <span class="text-xl font-bold text-white" data-editable="">ByteWise</span>
                        </div>
                        <p class="text-sm" data-editable="">Transforming education with AI-powered chatbots for personalized learning experiences.</p>
                    </div>
                    
                    <div>
                        <h4 class="font-semibold text-white mb-4 elegant-font" data-editable="">Quick Links</h4>
                        <ul class="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable-link="">About</a></li>
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable-link="">Help Center</a></li>
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable-link="">Privacy</a></li>
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable-link="">Terms</a></li>
                        </ul>
                    </div>
                </div>
                
                <div class="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
                    <p class="text-sm" data-editable="">© 2024 ByteWise. All rights reserved.</p>
                </div>
            </div>
        </footer>
    </div>

    <script>
        function editableApp() {
            return {
                darkMode: false,
                mobileMenu: false,
                saveStatus: '',

                init() {
                    console.log('Initializing editableApp...'); // 调试日志

                    this.darkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
                    this.$watch('darkMode', value => {
                        if (value) {
                            document.documentElement.classList.add('dark');
                        } else {
                            document.documentElement.classList.remove('dark');
                        }
                    });

                    this.enableDoubleClickEdit();
                    this.checkForBackup();
                    this.showStorageNotice();
                },

                checkForBackup() {
                    const backup = this.loadFromLocalStorage();
                    if (backup && backup.timestamp) {
                        const backupDate = new Date(backup.timestamp);
                        const now = new Date();
                        const hoursDiff = (now - backupDate) / (1000 * 60 * 60);

                        // 如果备份是在24小时内创建的，提示用户恢复
                        if (hoursDiff < 24) {
                            setTimeout(() => {
                                this.showNotification(`发现 ${Math.round(hoursDiff)} 小时前的备份。如需恢复，请打开浏览器控制台输入: editableApp().restoreBackup()`, 10000);
                            }, 2000);
                        }
                    }
                },

                restoreBackup() {
                    const backup = this.loadFromLocalStorage();
                    if (backup && backup.html) {
                        if (confirm('确定要恢复之前的备份吗？当前的修改将会丢失。')) {
                            document.open();
                            document.write(backup.html);
                            document.close();
                            this.showNotification('备份已恢复！');
                        }
                    } else {
                        this.showNotification('没有找到可用的备份。', 3000);
                    }
                },



                enableDoubleClickEdit() {
                    document.addEventListener('dblclick', (e) => {
                        const target = e.target.closest('[data-editable]');
                        const linkTarget = e.target.closest('[data-editable-link]');
                        
                        if (linkTarget) {
                            e.preventDefault();
                            e.stopPropagation();
                            this.editLink(linkTarget);
                            return;
                        }
                        
                        if (target) {
                            const originalContent = target.innerHTML;
                            
                            const originalBackground = target.style.background;
                            const originalWebkitBackground = target.style.webkitBackgroundClip;
                            const originalBackgroundClip = target.style.backgroundClip;
                            const originalWebkitTextFill = target.style.webkitTextFillColor;
                            const originalColor = target.style.color;
                            
                            const hasGradient = target.classList.contains('smooth-tech-gradient');
                            if (hasGradient) {
                                target.style.background = 'none';
                                target.style.webkitBackgroundClip = 'initial';
                                target.style.backgroundClip = 'initial';
                                target.style.webkitTextFillColor = 'initial';
                                target.style.color = '#6366F1';
                            }
                            
                            target.contentEditable = true;
                            target.focus();
                            target.style.outline = '2px solid #0EA5E9';
                            target.style.outlineOffset = '2px';
                            target.style.background = hasGradient ? 'rgba(14, 165, 233, 0.05)' : 'rgba(14, 165, 233, 0.05)';
                            
                            const range = document.createRange();
                            range.selectNodeContents(target);
                            const selection = window.getSelection();
                            selection.removeAllRanges();
                            selection.addRange(range);
                            
                            const exitEdit = () => {
                                target.contentEditable = false;
                                target.style.outline = '';
                                target.style.outlineOffset = '';
                                target.style.background = originalBackground;
                                
                                if (hasGradient) {
                                    target.style.webkitBackgroundClip = originalWebkitBackground;
                                    target.style.backgroundClip = originalBackgroundClip;
                                    target.style.webkitTextFillColor = originalWebkitTextFill;
                                    target.style.color = originalColor;
                                }
                                
                                target.removeEventListener('blur', exitEdit);
                                target.removeEventListener('keydown', handleKeydown);
                            };
                            
                            const handleKeydown = (e) => {
                                if (e.key === 'Enter' && !e.shiftKey) {
                                    e.preventDefault();
                                    target.blur();
                                } else if (e.key === 'Escape') {
                                    target.innerHTML = originalContent;
                                    target.blur();
                                }
                            };
                            
                            target.addEventListener('blur', exitEdit);
                            target.addEventListener('keydown', handleKeydown);
                        }
                    });
                },

                editLink(linkElement) {
                    const originalText = linkElement.textContent ? linkElement.textContent.trim() : '';
                    const originalHref = linkElement.href || '#';
                    
                    const modal = document.createElement('div');
                    modal.style.cssText = `
                        position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                        background: rgba(0,0,0,0.5); display: flex; align-items: center;
                        justify-content: center; z-index: 9999;
                    `;
                    
                    const modalContent = document.createElement('div');
                    modalContent.style.cssText = `
                        background: white; padding: 2rem; border-radius: 12px;
                        max-width: 400px; width: 90%; box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                    `;
                    modalContent.className = 'dark:bg-gray-800';
                    
                    modalContent.innerHTML = `
                        <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 1.5rem; color: #111827;" class="dark:text-gray-100">Edit Link</h3>
                        <div style="margin-bottom: 1rem;">
                            <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;" class="dark:text-gray-300">Link Text</label>
                            <input type="text" id="linkText" value="${originalText}" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 8px; font-size: 1rem;" class="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100">
                        </div>
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;" class="dark:text-gray-300">Link URL</label>
                            <input type="url" id="linkUrl" value="${originalHref}" placeholder="https://example.com" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 8px; font-size: 1rem;" class="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100">
                        </div>
                        <div style="display: flex; justify-content: flex-end; gap: 0.75rem;">
                            <button id="cancelBtn" style="padding: 0.5rem 1rem; color: #6b7280; background: none; border: none; cursor: pointer;" class="dark:text-gray-400">Cancel</button>
                            <button id="saveBtn" style="padding: 0.5rem 1rem; background: #0EA5E9; color: white; border: none; border-radius: 6px; cursor: pointer;">Save</button>
                        </div>
                    `;
                    
                    modal.appendChild(modalContent);
                    document.body.appendChild(modal);
                    
                    const textInput = document.getElementById('linkText');
                    const urlInput = document.getElementById('linkUrl');
                    
                    textInput.focus();
                    textInput.select();
                    
                    const saveChanges = () => {
                        const newText = textInput.value.trim();
                        const newUrl = urlInput.value.trim();
                        
                        if (newText) {
                            linkElement.textContent = newText;
                        }
                        
                        if (newUrl && newUrl !== '#') {
                            linkElement.href = newUrl;
                            linkElement.target = '_blank';
                            linkElement.rel = 'noopener noreferrer';
                        } else {
                            linkElement.href = '#';
                            linkElement.removeAttribute('target');
                            linkElement.removeAttribute('rel');
                        }
                        
                        modal.remove();
                    };
                    
                    document.getElementById('cancelBtn').onclick = () => modal.remove();
                    document.getElementById('saveBtn').onclick = saveChanges;
                    
                    modal.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            saveChanges();
                        } else if (e.key === 'Escape') {
                            modal.remove();
                        }
                    });
                    
                    modal.addEventListener('click', (e) => {
                        if (e.target === modal) {
                            modal.remove();
                        }
                    });
                },

                saveSection(sectionId) {
                    console.log('Saving section:', sectionId); // 调试日志

                    // 显示保存状态
                    this.saveStatus = '正在保存...';

                    // 获取保存按钮并更新视觉反馈
                    const saveBtn = document.querySelector(`#${sectionId} .save-btn`);
                    if (saveBtn) {
                        const originalText = saveBtn.innerHTML;
                        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
                        saveBtn.disabled = true;

                        setTimeout(() => {
                            saveBtn.innerHTML = '<i class="fas fa-check"></i> 已保存';
                            saveBtn.style.background = '#10B981';

                            setTimeout(() => {
                                saveBtn.innerHTML = originalText;
                                saveBtn.style.background = '#0EA5E9';
                                // saveBtn.disabled = false;
                            }, 1500);
                        }, 500);
                    }

                    // 实际保存功能：保存到localStorage作为备份
                    setTimeout(() => {
                        try {
                            const success = this.saveToLocalStorage();
                            if (success) {
                                this.saveStatus = '已保存到本地';
                                console.log('Section saved to localStorage'); // 调试日志
                            } else {
                                this.saveStatus = '保存失败';
                            }
                        } catch (error) {
                            console.error('Failed to save to localStorage:', error);
                            this.saveStatus = '保存出错';
                        }

                        // 3秒后隐藏状态
                        setTimeout(() => {
                            this.saveStatus = '';
                        }, 3000);

                        // 显示通知
                        this.showNotification('修改已确认！要永久保存请点击右下角"Download HTML"按钮下载文件。');
                    }, 500);
                },

                showStorageNotice() {
                    setTimeout(() => {
                        this.showNotification('提示：在Canvas环境中修改只在当前会话有效。要永久保存，请使用右下角的"Download HTML"按钮。', 8000);
                    }, 1000);
                },

                saveToLocalStorage() {
                    // 保存当前页面状态到localStorage作为备份
                    try {
                        console.log('Attempting to save to localStorage...'); // 调试日志

                        // 检查localStorage是否可用
                        if (typeof(Storage) === "undefined") {
                            throw new Error('localStorage not supported');
                        }

                        const currentHTML = document.documentElement.outerHTML;
                        const timestamp = new Date().toISOString();
                        const saveData = {
                            html: currentHTML,
                            timestamp: timestamp,
                            url: window.location.href
                        };

                        // 保存到localStorage
                        localStorage.setItem('bytewise-backup', JSON.stringify(saveData));
                        console.log('Successfully saved to localStorage at:', timestamp);

                        return true;
                    } catch (e) {
                        console.warn('Failed to save to localStorage:', e.message);
                        // 在某些环境中localStorage可能不可用（如iframe、隐私模式等）
                        return false;
                    }
                },

                loadFromLocalStorage() {
                    try {
                        const savedData = localStorage.getItem('bytewise-backup');
                        if (savedData) {
                            const data = JSON.parse(savedData);
                            console.log('Found backup from:', data.timestamp);
                            return data;
                        }
                        return null;
                    } catch (e) {
                        console.warn('Failed to load from localStorage:', e.message);
                        return null;
                    }
                },

                showNotification(message, duration = 4000) {
                    console.log('Showing notification:', message); // 调试日志

                    // 移除现有的通知
                    const existingNotifications = document.querySelectorAll('.custom-notification');
                    existingNotifications.forEach(n => n.remove());

                    const notification = document.createElement('div');
                    notification.className = 'custom-notification';
                    notification.style.cssText = `
                        position: fixed; top: 20px; right: 20px; background: #10B981;
                        color: white; padding: 12px 20px; border-radius: 8px;
                        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3); z-index: 10000;
                        font-size: 14px; max-width: 300px; opacity: 0;
                        transform: translateY(-20px); transition: all 0.3s ease;
                        font-family: 'Work Sans', sans-serif; line-height: 1.4;
                    `;
                    notification.innerHTML = `<i class="fas fa-info-circle" style="margin-right: 8px;"></i>${message}`;

                    document.body.appendChild(notification);
                    console.log('Notification added to DOM'); // 调试日志

                    // 强制重绘后显示动画
                    requestAnimationFrame(() => {
                        notification.style.opacity = '1';
                        notification.style.transform = 'translateY(0)';
                    });

                    // 自动隐藏
                    setTimeout(() => {
                        notification.style.opacity = '0';
                        notification.style.transform = 'translateY(-20px)';
                        setTimeout(() => {
                            if (document.body.contains(notification)) {
                                document.body.removeChild(notification);
                            }
                        }, 300);
                    }, duration);
                },

                downloadHTML() {
                    const docClone = document.documentElement.cloneNode(true);
                    
                    const editControls = docClone.querySelectorAll('.edit-controls, .download-btn, .edit-mode-toggle');
                    editControls.forEach(el => el.remove());
                    
                    const editableSections = docClone.querySelectorAll('.editable-section');
                    editableSections.forEach(section => {
                        section.classList.remove('editing');
                        const editableElements = section.querySelectorAll('[data-editable]');
                        editableElements.forEach(el => {
                            el.removeAttribute('contenteditable');
                        });
                    });
                    
                    const htmlContent = '<!DOCTYPE html>\n' + docClone.outerHTML;
                    const blob = new Blob([htmlContent], { type: 'text/html' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'bytewise-edited.html';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }
            };
        }

        // Dark mode detection
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (event.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        //打开后弹窗显示是否恢复登录
        document.addEventListener('DOMContentLoaded', function() {
            if (confirm('是否恢复之前的编辑？')) {
                editableApp().restoreBackup();
            }
        });
    </script>




</body></html>